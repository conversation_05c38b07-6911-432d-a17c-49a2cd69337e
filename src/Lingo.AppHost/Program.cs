var builder = DistributedApplication.CreateBuilder(args);

// Add PostgreSQL database
var postgres = builder.AddPostgres("postgres")
    .WithDataVolume()
    .PublishAsConnectionString()
    .WithPgAdmin();

var lingoDb = postgres.AddDatabase("lingodb")
    ;

// Add Redis cache
var redis = builder.AddRedis("redis")
    .WithDataVolume();

// Add the main Lingo application
builder.AddProject<Projects.Lingo>("lingo")
    .WithReference(lingoDb)
    .WithReference(redis);

builder.Build().Run();
