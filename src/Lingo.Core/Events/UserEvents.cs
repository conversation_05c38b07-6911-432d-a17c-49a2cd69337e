using Lingo.Core.Models;

namespace Lingo.Core.Events;

public record UserRegisteredEvent(
    long UserId,
    string Email,
    string DisplayName,
    DateTime RegisteredAt
);

public record LessonCompletedEvent(
    long UserId,
    long LessonId,
    long CourseId,
    int Score,
    int MaxScore,
    int ExperiencePointsEarned,
    DateTime CompletedAt
);

public record BadgeEarnedEvent(
    long UserId,
    string BadgeId,
    string BadgeName,
    BadgeType BadgeType,
    DateTime EarnedAt
);

public record StreakUpdatedEvent(
    long UserId,
    int CurrentStreak,
    int LongestStreak,
    DateTime UpdatedAt
);

public record CourseCompletedEvent(
    long UserId,
    long CourseId,
    string CourseName,
    DifficultyLevel Difficulty,
    DateTime CompletedAt
);

public record ExperiencePointsEarnedEvent(
    long UserId,
    int PointsEarned,
    int TotalPoints,
    string Source,
    DateTime EarnedAt
);
