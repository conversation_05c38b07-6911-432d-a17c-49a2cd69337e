using Lingo.Core.Models;
using Orleans;

namespace Lingo.Core.Interfaces;

public interface IUserGrain : IGrainWithIntegerKey
{
    Task<User?> GetUserAsync();
    Task<User> CreateUserAsync(string email, string displayName);
    Task UpdateUserAsync(User user);
    Task AddExperiencePointsAsync(int points, string source);
    Task UpdateStreakAsync();
    Task AddBadgeAsync(Badge badge);
    Task<List<Badge>> GetBadgesAsync();
    Task<UserCourseProgress?> GetCourseProgressAsync(long courseId);
    Task UpdateCourseProgressAsync(long courseId, int completedLessons, int totalLessons);
}

public interface ICourseGrain : IGrainWithIntegerKey
{
    Task<Course?> GetCourseAsync();
    Task<List<Lesson>> GetLessonsAsync();
    Task<Lesson?> GetLessonAsync(long lessonId);
    Task<List<LessonQuestion>> GetLessonQuestionsAsync(long lessonId);
}

public interface ILessonGrain : IGrainWithIntegerKey
{
    Task<Lesson?> GetLessonAsync();
    Task<List<LessonQuestion>> GetQuestionsAsync();
    Task<UserLessonProgress?> GetUserProgressAsync(long userId);
    Task CompleteUserLessonAsync(long userId, int score, int maxScore);
}

public interface ILearningService : IGrainWithIntegerKey
{
    Task<List<Course>> GetAvailableCoursesAsync();
    Task<Course?> GetCourseWithLessonsAsync(long courseId);
    Task<UserLessonProgress> SubmitLessonAnswersAsync(long userId, long lessonId, Dictionary<long, List<long>> answers);
    Task<List<UserCourseProgress>> GetUserProgressAsync(long userId);
}
