namespace Lingo.Core.Models;

public class Course
{
    public long Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Language { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = string.Empty;
    public DifficultyLevel Difficulty { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public List<Lesson> Lessons { get; set; } = new();
}

public enum DifficultyLevel
{
    Beginner = 1,
    Intermediate = 2,
    Advanced = 3
}

public class Lesson
{
    public long Id { get; set; }
    public long CourseId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int OrderIndex { get; set; }
    public int ExperiencePoints { get; set; } = 10;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public Course Course { get; set; } = null!;
    public List<LessonQuestion> Questions { get; set; } = new();
}

public class LessonQuestion
{
    public long Id { get; set; }
    public long LessonId { get; set; }
    public string QuestionText { get; set; } = string.Empty;
    public string? AudioUrl { get; set; }
    public string? ImageUrl { get; set; }
    public QuestionType Type { get; set; }
    public int OrderIndex { get; set; }
    public Lesson Lesson { get; set; } = null!;
    public List<QuestionOption> Options { get; set; } = new();
}

public enum QuestionType
{
    MultipleChoice,
    Translation,
    ListeningComprehension,
    SpeakingPractice,
    FillInTheBlank,
    MatchingPairs
}

public class QuestionOption
{
    public long Id { get; set; }
    public long QuestionId { get; set; }
    public string OptionText { get; set; } = string.Empty;
    public string? AudioUrl { get; set; }
    public string? ImageUrl { get; set; }
    public bool IsCorrect { get; set; }
    public int OrderIndex { get; set; }
    public LessonQuestion Question { get; set; } = null!;
}

public class UserLessonProgress
{
    public long Id { get; set; }
    public long UserId { get; set; }
    public long LessonId { get; set; }
    public bool IsCompleted { get; set; }
    public int Score { get; set; }
    public int MaxScore { get; set; }
    public int AttemptsCount { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime LastAttemptAt { get; set; }
    public DateTime CreatedAt { get; set; }
}
