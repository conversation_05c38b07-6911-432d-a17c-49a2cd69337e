namespace Lingo.Core.Models;

public class User
{
    public long Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? ProfileImageUrl { get; set; }
    public int ExperiencePoints { get; set; }
    public int CurrentStreak { get; set; }
    public int LongestStreak { get; set; }
    public DateTime LastActivityDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<Badge> Badges { get; set; } = new();
    public List<UserCourseProgress> CourseProgress { get; set; } = new();
}

public class Badge
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IconUrl { get; set; } = string.Empty;
    public DateTime EarnedAt { get; set; }
    public BadgeType Type { get; set; }
}

public enum BadgeType
{
    LessonCompletion,
    Streak,
    ExperiencePoints,
    CourseCompletion,
    PerfectScore,
    FirstLesson,
    Dedication
}

public class UserCourseProgress
{
    public long UserId { get; set; }
    public long CourseId { get; set; }
    public int CompletedLessons { get; set; }
    public int TotalLessons { get; set; }
    public DateTime LastAccessedAt { get; set; }
    public bool IsCompleted { get; set; }
    public double CompletionPercentage => TotalLessons > 0 ? (double)CompletedLessons / TotalLessons * 100 : 0;
}
