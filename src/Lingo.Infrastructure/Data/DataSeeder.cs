using Lingo.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Lingo.Infrastructure.Data;

public class DataSeeder
{
    private readonly LingoDbContext _context;
    private readonly ILogger<DataSeeder> _logger;

    public DataSeeder(LingoDbContext context, ILogger<DataSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            await _context.Database.EnsureCreatedAsync();

            if (await _context.Courses.AnyAsync())
            {
                _logger.LogInformation("Database already seeded");
                return;
            }

            _logger.LogInformation("Starting database seeding...");

            await SeedCoursesAsync();
            await _context.SaveChangesAsync();

            _logger.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while seeding database");
            throw;
        }
    }

    private async Task SeedCoursesAsync()
    {
        var languages = new[]
        {
            new { Name = "Spanish", Code = "es", Image = "/images/flags/spain.png" },
            new { Name = "French", Code = "fr", Image = "/images/flags/france.png" },
            new { Name = "German", Code = "de", Image = "/images/flags/germany.png" },
            new { Name = "Italian", Code = "it", Image = "/images/flags/italy.png" }
        };

        var difficulties = new[] { DifficultyLevel.Beginner, DifficultyLevel.Intermediate, DifficultyLevel.Advanced };

        foreach (var language in languages)
        {
            foreach (var difficulty in difficulties)
            {
                var course = new Course
                {
                    Name = $"{language.Name} - {difficulty}",
                    Description = $"Learn {language.Name} at {difficulty} level",
                    Language = language.Name,
                    LanguageCode = language.Code,
                    Difficulty = difficulty,
                    ImageUrl = language.Image,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    Lessons = new List<Lesson>()
                };

                // Create 25 lessons for each course
                for (int i = 1; i <= 25; i++)
                {
                    var lesson = new Lesson
                    {
                        Title = $"Lesson {i}: {GetLessonTitle(difficulty, i)}",
                        Description = $"Learn {GetLessonDescription(difficulty, i)} in {language.Name}",
                        OrderIndex = i,
                        ExperiencePoints = GetExperiencePoints(difficulty),
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        Questions = CreateLessonQuestions(language.Code, difficulty, i)
                    };

                    course.Lessons.Add(lesson);
                }

                _context.Courses.Add(course);
            }
        }
    }

    private static string GetLessonTitle(DifficultyLevel difficulty, int lessonNumber)
    {
        return difficulty switch
        {
            DifficultyLevel.Beginner => lessonNumber switch
            {
                1 => "Basic Greetings",
                2 => "Numbers 1-10",
                3 => "Colors",
                4 => "Family Members",
                5 => "Days of the Week",
                6 => "Food and Drinks",
                7 => "Animals",
                8 => "Body Parts",
                9 => "Clothing",
                10 => "Weather",
                11 => "Time",
                12 => "Directions",
                13 => "Transportation",
                14 => "School Subjects",
                15 => "Hobbies",
                16 => "Emotions",
                17 => "House and Home",
                18 => "Shopping",
                19 => "Restaurant",
                20 => "Health",
                21 => "Sports",
                22 => "Music",
                23 => "Technology",
                24 => "Travel",
                _ => "Review and Practice"
            },
            DifficultyLevel.Intermediate => lessonNumber switch
            {
                1 => "Past Tense Basics",
                2 => "Future Plans",
                3 => "Comparisons",
                4 => "Conditional Statements",
                5 => "Complex Sentences",
                6 => "Business Vocabulary",
                7 => "Cultural Expressions",
                8 => "Formal vs Informal",
                9 => "Storytelling",
                10 => "Debates and Opinions",
                11 => "News and Media",
                12 => "Science and Nature",
                13 => "History and Politics",
                14 => "Art and Literature",
                15 => "Philosophy",
                16 => "Economics",
                17 => "Environment",
                18 => "Social Issues",
                19 => "Technology Trends",
                20 => "Global Culture",
                21 => "Professional Communication",
                22 => "Academic Writing",
                23 => "Public Speaking",
                24 => "Critical Thinking",
                _ => "Advanced Review"
            },
            DifficultyLevel.Advanced => lessonNumber switch
            {
                1 => "Subjunctive Mood",
                2 => "Complex Grammar",
                3 => "Idiomatic Expressions",
                4 => "Literary Analysis",
                5 => "Advanced Vocabulary",
                6 => "Nuanced Communication",
                7 => "Regional Dialects",
                8 => "Historical Context",
                9 => "Philosophical Discourse",
                10 => "Technical Writing",
                11 => "Poetry and Prose",
                12 => "Academic Research",
                13 => "Professional Presentations",
                14 => "Negotiation Skills",
                15 => "Cultural Sensitivity",
                16 => "Advanced Listening",
                17 => "Debate Techniques",
                18 => "Creative Writing",
                19 => "Translation Skills",
                20 => "Interpretation",
                21 => "Specialized Terminology",
                22 => "Cross-cultural Communication",
                23 => "Advanced Pronunciation",
                24 => "Fluency Development",
                _ => "Mastery Assessment"
            },
            _ => $"Lesson {lessonNumber}"
        };
    }

    private static string GetLessonDescription(DifficultyLevel difficulty, int lessonNumber)
    {
        return difficulty switch
        {
            DifficultyLevel.Beginner => "basic vocabulary and simple phrases",
            DifficultyLevel.Intermediate => "intermediate grammar and conversation skills",
            DifficultyLevel.Advanced => "advanced concepts and fluent communication",
            _ => "language skills"
        };
    }

    private static int GetExperiencePoints(DifficultyLevel difficulty)
    {
        return difficulty switch
        {
            DifficultyLevel.Beginner => 10,
            DifficultyLevel.Intermediate => 15,
            DifficultyLevel.Advanced => 20,
            _ => 10
        };
    }

    private static List<LessonQuestion> CreateLessonQuestions(string languageCode, DifficultyLevel difficulty, int lessonNumber)
    {
        var questions = new List<LessonQuestion>();

        // Create 5 questions per lesson
        for (int i = 1; i <= 5; i++)
        {
            var question = new LessonQuestion
            {
                QuestionText = GetQuestionText(languageCode, difficulty, lessonNumber, i),
                Type = GetQuestionType(i),
                OrderIndex = i,
                Options = CreateQuestionOptions(languageCode, difficulty, lessonNumber, i)
            };

            questions.Add(question);
        }

        return questions;
    }

    private static string GetQuestionText(string languageCode, DifficultyLevel difficulty, int lessonNumber, int questionNumber)
    {
        // Simplified question generation - in a real app, this would be much more sophisticated
        return languageCode switch
        {
            "es" => difficulty switch
            {
                DifficultyLevel.Beginner => $"¿Cómo se dice 'hello' en español?",
                DifficultyLevel.Intermediate => $"¿Cuál es la forma correcta del verbo en pasado?",
                DifficultyLevel.Advanced => $"¿Qué significa esta expresión idiomática?",
                _ => "¿Cuál es la respuesta correcta?"
            },
            "fr" => difficulty switch
            {
                DifficultyLevel.Beginner => $"Comment dit-on 'hello' en français?",
                DifficultyLevel.Intermediate => $"Quelle est la forme correcte du verbe au passé?",
                DifficultyLevel.Advanced => $"Que signifie cette expression idiomatique?",
                _ => "Quelle est la bonne réponse?"
            },
            "de" => difficulty switch
            {
                DifficultyLevel.Beginner => $"Wie sagt man 'hello' auf Deutsch?",
                DifficultyLevel.Intermediate => $"Was ist die richtige Vergangenheitsform?",
                DifficultyLevel.Advanced => $"Was bedeutet diese Redewendung?",
                _ => "Was ist die richtige Antwort?"
            },
            "it" => difficulty switch
            {
                DifficultyLevel.Beginner => $"Come si dice 'hello' in italiano?",
                DifficultyLevel.Intermediate => $"Qual è la forma corretta del verbo al passato?",
                DifficultyLevel.Advanced => $"Cosa significa questa espressione idiomatica?",
                _ => "Qual è la risposta corretta?"
            },
            _ => $"What is the correct answer for question {questionNumber}?"
        };
    }

    private static QuestionType GetQuestionType(int questionNumber)
    {
        return questionNumber switch
        {
            1 => QuestionType.MultipleChoice,
            2 => QuestionType.Translation,
            3 => QuestionType.FillInTheBlank,
            4 => QuestionType.MatchingPairs,
            5 => QuestionType.ListeningComprehension,
            _ => QuestionType.MultipleChoice
        };
    }

    private static List<QuestionOption> CreateQuestionOptions(string languageCode, DifficultyLevel difficulty, int lessonNumber, int questionNumber)
    {
        var options = new List<QuestionOption>();

        // Create 4 options with one correct answer
        var correctAnswers = GetCorrectAnswers(languageCode, difficulty, lessonNumber, questionNumber);
        var incorrectAnswers = GetIncorrectAnswers(languageCode, difficulty, lessonNumber, questionNumber);

        // Add correct answer
        options.Add(new QuestionOption
        {
            OptionText = correctAnswers[0],
            IsCorrect = true,
            OrderIndex = 1
        });

        // Add incorrect answers
        for (int i = 0; i < Math.Min(3, incorrectAnswers.Length); i++)
        {
            options.Add(new QuestionOption
            {
                OptionText = incorrectAnswers[i],
                IsCorrect = false,
                OrderIndex = i + 2
            });
        }

        return options;
    }

    private static string[] GetCorrectAnswers(string languageCode, DifficultyLevel difficulty, int lessonNumber, int questionNumber)
    {
        return languageCode switch
        {
            "es" => new[] { "Hola" },
            "fr" => new[] { "Bonjour" },
            "de" => new[] { "Hallo" },
            "it" => new[] { "Ciao" },
            _ => new[] { "Hello" }
        };
    }

    private static string[] GetIncorrectAnswers(string languageCode, DifficultyLevel difficulty, int lessonNumber, int questionNumber)
    {
        return languageCode switch
        {
            "es" => new[] { "Adiós", "Gracias", "Por favor" },
            "fr" => new[] { "Au revoir", "Merci", "S'il vous plaît" },
            "de" => new[] { "Auf Wiedersehen", "Danke", "Bitte" },
            "it" => new[] { "Arrivederci", "Grazie", "Prego" },
            _ => new[] { "Goodbye", "Thank you", "Please" }
        };
    }
}
