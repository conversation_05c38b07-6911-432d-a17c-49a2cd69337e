-- Initial database schema for Lingo application

-- Create Courses table
CREATE TABLE IF NOT EXISTS "Courses" (
    "Id" BIGSERIAL PRIMARY KEY,
    "Name" VARCHAR(200) NOT NULL,
    "Description" VARCHAR(1000),
    "Language" VARCHAR(100) NOT NULL,
    "LanguageCode" VARCHAR(10) NOT NULL,
    "Difficulty" INTEGER NOT NULL,
    "ImageUrl" VARCHAR(500),
    "IsActive" BOOLEAN NOT NULL DEFAULT TRUE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create Lessons table
CREATE TABLE IF NOT EXISTS "Lessons" (
    "Id" BIGSERIAL PRIMARY KEY,
    "CourseId" BIGINT NOT NULL,
    "Title" VARCHAR(200) NOT NULL,
    "Description" VARCHAR(1000),
    "OrderIndex" INTEGER NOT NULL,
    "ExperiencePoints" INTEGER NOT NULL DEFAULT 10,
    "IsActive" BOOLEAN NOT NULL DEFAULT TRUE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    FOREIGN KEY ("CourseId") REFERENCES "Courses"("Id") ON DELETE CASCADE
);

-- Create LessonQuestions table
CREATE TABLE IF NOT EXISTS "LessonQuestions" (
    "Id" BIGSERIAL PRIMARY KEY,
    "LessonId" BIGINT NOT NULL,
    "QuestionText" VARCHAR(1000) NOT NULL,
    "AudioUrl" VARCHAR(500),
    "ImageUrl" VARCHAR(500),
    "Type" INTEGER NOT NULL,
    "OrderIndex" INTEGER NOT NULL,
    FOREIGN KEY ("LessonId") REFERENCES "Lessons"("Id") ON DELETE CASCADE
);

-- Create QuestionOptions table
CREATE TABLE IF NOT EXISTS "QuestionOptions" (
    "Id" BIGSERIAL PRIMARY KEY,
    "QuestionId" BIGINT NOT NULL,
    "OptionText" VARCHAR(500) NOT NULL,
    "AudioUrl" VARCHAR(500),
    "ImageUrl" VARCHAR(500),
    "IsCorrect" BOOLEAN NOT NULL DEFAULT FALSE,
    "OrderIndex" INTEGER NOT NULL,
    FOREIGN KEY ("QuestionId") REFERENCES "LessonQuestions"("Id") ON DELETE CASCADE
);

-- Create UserLessonProgress table
CREATE TABLE IF NOT EXISTS "UserLessonProgress" (
    "Id" BIGSERIAL PRIMARY KEY,
    "UserId" BIGINT NOT NULL,
    "LessonId" BIGINT NOT NULL,
    "IsCompleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "Score" INTEGER NOT NULL DEFAULT 0,
    "MaxScore" INTEGER NOT NULL DEFAULT 0,
    "AttemptsCount" INTEGER NOT NULL DEFAULT 0,
    "CompletedAt" TIMESTAMP WITH TIME ZONE,
    "LastAttemptAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE("UserId", "LessonId")
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "IX_Courses_LanguageCode" ON "Courses"("LanguageCode");
CREATE INDEX IF NOT EXISTS "IX_Courses_Difficulty" ON "Courses"("Difficulty");
CREATE INDEX IF NOT EXISTS "IX_Lessons_CourseId_OrderIndex" ON "Lessons"("CourseId", "OrderIndex");
CREATE INDEX IF NOT EXISTS "IX_LessonQuestions_LessonId_OrderIndex" ON "LessonQuestions"("LessonId", "OrderIndex");
CREATE INDEX IF NOT EXISTS "IX_QuestionOptions_QuestionId_OrderIndex" ON "QuestionOptions"("QuestionId", "OrderIndex");
CREATE INDEX IF NOT EXISTS "IX_UserLessonProgress_UserId" ON "UserLessonProgress"("UserId");
CREATE INDEX IF NOT EXISTS "IX_UserLessonProgress_LessonId" ON "UserLessonProgress"("LessonId");
