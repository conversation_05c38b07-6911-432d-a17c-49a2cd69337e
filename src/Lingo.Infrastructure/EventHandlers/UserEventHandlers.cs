using Lingo.Core.Events;
using Microsoft.Extensions.Logging;

namespace Lingo.Infrastructure.EventHandlers;

public class UserEventHandlers
{
    private readonly ILogger<UserEventHandlers> _logger;

    public UserEventHandlers(ILogger<UserEventHandlers> logger)
    {
        _logger = logger;
    }

    public async Task Handle(UserRegisteredEvent @event)
    {
        _logger.LogInformation("User registered: {UserId} - {Email} - {DisplayName}", 
            @event.UserId, @event.Email, @event.DisplayName);

        // Could send welcome email, create default preferences, etc.
        await Task.CompletedTask;
    }

    public async Task Handle(LessonCompletedEvent @event)
    {
        _logger.LogInformation("Lesson completed: User {UserId} completed lesson {LessonId} with score {Score}/{MaxScore}", 
            @event.UserId, @event.LessonId, @event.Score, @event.MaxScore);

        // Could trigger achievements, send notifications, update analytics, etc.
        await Task.CompletedTask;
    }

    public async Task Handle(BadgeEarnedEvent @event)
    {
        _logger.LogInformation("Badge earned: User {UserId} earned badge {BadgeName} ({BadgeType})", 
            @event.UserId, @event.BadgeName, @event.BadgeType);

        // Could send push notification, update social feed, etc.
        await Task.CompletedTask;
    }

    public async Task Handle(StreakUpdatedEvent @event)
    {
        _logger.LogInformation("Streak updated: User {UserId} has {CurrentStreak} day streak (longest: {LongestStreak})", 
            @event.UserId, @event.CurrentStreak, @event.LongestStreak);

        // Could send encouragement notifications, update leaderboards, etc.
        await Task.CompletedTask;
    }

    public async Task Handle(CourseCompletedEvent @event)
    {
        _logger.LogInformation("Course completed: User {UserId} completed course {CourseName} ({Difficulty})", 
            @event.UserId, @event.CourseName, @event.Difficulty);

        // Could award completion certificate, unlock new courses, etc.
        await Task.CompletedTask;
    }

    public async Task Handle(ExperiencePointsEarnedEvent @event)
    {
        _logger.LogInformation("XP earned: User {UserId} earned {PointsEarned} XP from {Source} (total: {TotalPoints})", 
            @event.UserId, @event.PointsEarned, @event.Source, @event.TotalPoints);

        // Could update leaderboards, check for level ups, etc.
        await Task.CompletedTask;
    }
}
