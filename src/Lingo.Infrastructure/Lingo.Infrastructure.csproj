<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
    <PackageReference Include="Marten" Version="8.3.0" />
    <PackageReference Include="Marten.AspNetCore" Version="8.3.0" />
    <PackageReference Include="Microsoft.Orleans.Core" Version="9.0.0" />
    <PackageReference Include="Microsoft.Orleans.Serialization" Version="9.0.0" />
    <PackageReference Include="WolverineFx" Version="4.3.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Lingo.Core\Lingo.Core.csproj" />
  </ItemGroup>

</Project>
