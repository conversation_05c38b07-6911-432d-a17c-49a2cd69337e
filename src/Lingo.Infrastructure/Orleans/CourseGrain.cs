using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Lingo.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Orleans;
using System.Text.Json;

namespace Lingo.Infrastructure.Orleans;

public class CourseGrain : Grain, ICourseGrain
{
    private readonly LingoDbContext _dbContext;
    private readonly IDistributedCache _cache;
    private readonly ILogger<CourseGrain> _logger;

    public CourseGrain(
        LingoDbContext dbContext,
        IDistributedCache cache,
        ILogger<CourseGrain> logger)
    {
        _dbContext = dbContext;
        _cache = cache;
        _logger = logger;
    }

    public async Task<Course?> GetCourseAsync()
    {
        var courseId = this.GetPrimaryKeyLong();
        var cacheKey = $"course:{courseId}";

        // Try cache first
        var cachedCourse = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedCourse))
        {
            return JsonSerializer.Deserialize<Course>(cachedCourse);
        }

        // Load from database
        var course = await _dbContext.Courses
            .FirstOrDefaultAsync(c => c.Id == courseId && c.IsActive);

        if (course != null)
        {
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(course),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
        }

        return course;
    }

    public async Task<List<Lesson>> GetLessonsAsync()
    {
        var courseId = this.GetPrimaryKeyLong();
        var cacheKey = $"course_lessons:{courseId}";

        // Try cache first
        var cachedLessons = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedLessons))
        {
            return JsonSerializer.Deserialize<List<Lesson>>(cachedLessons) ?? new List<Lesson>();
        }

        // Load from database
        var lessons = await _dbContext.Lessons
            .Where(l => l.CourseId == courseId && l.IsActive)
            .OrderBy(l => l.OrderIndex)
            .ToListAsync();

        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(lessons),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });

        return lessons;
    }

    public async Task<Lesson?> GetLessonAsync(long lessonId)
    {
        var cacheKey = $"lesson:{lessonId}";

        // Try cache first
        var cachedLesson = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedLesson))
        {
            return JsonSerializer.Deserialize<Lesson>(cachedLesson);
        }

        // Load from database
        var lesson = await _dbContext.Lessons
            .Include(l => l.Course)
            .FirstOrDefaultAsync(l => l.Id == lessonId && l.IsActive);

        if (lesson != null)
        {
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(lesson),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
        }

        return lesson;
    }

    public async Task<List<LessonQuestion>> GetLessonQuestionsAsync(long lessonId)
    {
        var cacheKey = $"lesson_questions:{lessonId}";

        // Try cache first
        var cachedQuestions = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedQuestions))
        {
            return JsonSerializer.Deserialize<List<LessonQuestion>>(cachedQuestions) ?? new List<LessonQuestion>();
        }

        // Load from database
        var questions = await _dbContext.LessonQuestions
            .Include(q => q.Options)
            .Where(q => q.LessonId == lessonId)
            .OrderBy(q => q.OrderIndex)
            .ToListAsync();

        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(questions),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });

        return questions;
    }
}
