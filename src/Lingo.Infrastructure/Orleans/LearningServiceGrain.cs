using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Lingo.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Orleans;
using System.Text.Json;

namespace Lingo.Infrastructure.Orleans;

public class LearningServiceGrain : Grain, ILearningService
{
    private readonly LingoDbContext _dbContext;
    private readonly IDistributedCache _cache;
    private readonly ILogger<LearningServiceGrain> _logger;

    public LearningServiceGrain(
        LingoDbContext dbContext,
        IDistributedCache cache,
        ILogger<LearningServiceGrain> logger)
    {
        _dbContext = dbContext;
        _cache = cache;
        _logger = logger;
    }

    public async Task<List<Course>> GetAvailableCoursesAsync()
    {
        const string cacheKey = "available_courses";

        // Try cache first
        var cachedCourses = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedCourses))
        {
            return JsonSerializer.Deserialize<List<Course>>(cachedCourses) ?? new List<Course>();
        }

        // Load from database
        var courses = await _dbContext.Courses
            .Where(c => c.IsActive)
            .OrderBy(c => c.Difficulty)
            .ThenBy(c => c.Name)
            .ToListAsync();

        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(courses),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(2) });

        return courses;
    }

    public async Task<Course?> GetCourseWithLessonsAsync(long courseId)
    {
        var cacheKey = $"course_with_lessons:{courseId}";

        // Try cache first
        var cachedCourse = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedCourse))
        {
            return JsonSerializer.Deserialize<Course>(cachedCourse);
        }

        // Load from database
        var course = await _dbContext.Courses
            .Include(c => c.Lessons.Where(l => l.IsActive))
            .FirstOrDefaultAsync(c => c.Id == courseId && c.IsActive);

        if (course != null)
        {
            // Sort lessons by order
            course.Lessons = course.Lessons.OrderBy(l => l.OrderIndex).ToList();

            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(course),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
        }

        return course;
    }

    public async Task<UserLessonProgress> SubmitLessonAnswersAsync(long userId, long lessonId, Dictionary<long, List<long>> answers)
    {
        // Get lesson questions with correct answers
        var questions = await _dbContext.LessonQuestions
            .Include(q => q.Options)
            .Where(q => q.LessonId == lessonId)
            .ToListAsync();

        if (!questions.Any())
        {
            throw new InvalidOperationException($"No questions found for lesson {lessonId}");
        }

        // Calculate score
        int correctAnswers = 0;
        int totalQuestions = questions.Count;

        foreach (var question in questions)
        {
            if (answers.TryGetValue(question.Id, out var userAnswers))
            {
                var correctOptions = question.Options.Where(o => o.IsCorrect).Select(o => o.Id).ToList();
                
                // Check if user's answers match correct answers exactly
                if (userAnswers.Count == correctOptions.Count && 
                    userAnswers.All(ua => correctOptions.Contains(ua)) &&
                    correctOptions.All(co => userAnswers.Contains(co)))
                {
                    correctAnswers++;
                }
            }
        }

        int score = correctAnswers;
        int maxScore = totalQuestions;

        // Update lesson progress through grain
        var lessonGrain = GrainFactory.GetGrain<ILessonGrain>(lessonId);
        await lessonGrain.CompleteUserLessonAsync(userId, score, maxScore);

        // Update course progress
        var lesson = await _dbContext.Lessons
            .Include(l => l.Course)
            .FirstOrDefaultAsync(l => l.Id == lessonId);

        if (lesson != null)
        {
            var completedLessonsCount = await _dbContext.UserLessonProgress
                .Join(_dbContext.Lessons, 
                      ulp => ulp.LessonId, 
                      l => l.Id, 
                      (ulp, l) => new { ulp, l })
                .Where(x => x.ulp.UserId == userId && 
                           x.l.CourseId == lesson.CourseId && 
                           x.ulp.IsCompleted)
                .CountAsync();

            var totalLessonsCount = await _dbContext.Lessons
                .Where(l => l.CourseId == lesson.CourseId && l.IsActive)
                .CountAsync();

            var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
            await userGrain.UpdateCourseProgressAsync(lesson.CourseId, completedLessonsCount, totalLessonsCount);
        }

        // Return the updated progress
        var progress = await lessonGrain.GetUserProgressAsync(userId);
        return progress ?? new UserLessonProgress
        {
            UserId = userId,
            LessonId = lessonId,
            Score = score,
            MaxScore = maxScore,
            IsCompleted = score > 0,
            AttemptsCount = 1,
            LastAttemptAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow
        };
    }

    public async Task<List<UserCourseProgress>> GetUserProgressAsync(long userId)
    {
        var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
        var user = await userGrain.GetUserAsync();
        
        return user?.CourseProgress ?? new List<UserCourseProgress>();
    }
}
