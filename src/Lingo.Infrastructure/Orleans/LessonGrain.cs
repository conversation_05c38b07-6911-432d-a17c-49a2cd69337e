using Lingo.Core.Events;
using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Lingo.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Orleans;
using System.Text.Json;
using Wolverine;

namespace Lingo.Infrastructure.Orleans;

public class LessonGrain : Grain, ILessonGrain
{
    private readonly LingoDbContext _dbContext;
    private readonly IDistributedCache _cache;
    private readonly IMessageBus _messageBus;
    private readonly ILogger<LessonGrain> _logger;

    public LessonGrain(
        LingoDbContext dbContext,
        IDistributedCache cache,
        IMessageBus messageBus,
        ILogger<LessonGrain> logger)
    {
        _dbContext = dbContext;
        _cache = cache;
        _messageBus = messageBus;
        _logger = logger;
    }

    public async Task<Lesson?> GetLessonAsync()
    {
        var lessonId = this.GetPrimaryKeyLong();
        var cacheKey = $"lesson:{lessonId}";

        // Try cache first
        var cachedLesson = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedLesson))
        {
            return JsonSerializer.Deserialize<Lesson>(cachedLesson);
        }

        // Load from database
        var lesson = await _dbContext.Lessons
            .Include(l => l.Course)
            .FirstOrDefaultAsync(l => l.Id == lessonId && l.IsActive);

        if (lesson != null)
        {
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(lesson),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
        }

        return lesson;
    }

    public async Task<List<LessonQuestion>> GetQuestionsAsync()
    {
        var lessonId = this.GetPrimaryKeyLong();
        var cacheKey = $"lesson_questions:{lessonId}";

        // Try cache first
        var cachedQuestions = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedQuestions))
        {
            return JsonSerializer.Deserialize<List<LessonQuestion>>(cachedQuestions) ?? new List<LessonQuestion>();
        }

        // Load from database
        var questions = await _dbContext.LessonQuestions
            .Include(q => q.Options)
            .Where(q => q.LessonId == lessonId)
            .OrderBy(q => q.OrderIndex)
            .ToListAsync();

        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(questions),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });

        return questions;
    }

    public async Task<UserLessonProgress?> GetUserProgressAsync(long userId)
    {
        var lessonId = this.GetPrimaryKeyLong();
        
        return await _dbContext.UserLessonProgress
            .FirstOrDefaultAsync(p => p.UserId == userId && p.LessonId == lessonId);
    }

    public async Task CompleteUserLessonAsync(long userId, int score, int maxScore)
    {
        var lessonId = this.GetPrimaryKeyLong();
        
        var progress = await _dbContext.UserLessonProgress
            .FirstOrDefaultAsync(p => p.UserId == userId && p.LessonId == lessonId);

        var isFirstCompletion = progress == null || !progress.IsCompleted;
        var now = DateTime.UtcNow;

        if (progress == null)
        {
            progress = new UserLessonProgress
            {
                UserId = userId,
                LessonId = lessonId,
                IsCompleted = score > 0,
                Score = score,
                MaxScore = maxScore,
                AttemptsCount = 1,
                CompletedAt = score > 0 ? now : null,
                LastAttemptAt = now,
                CreatedAt = now
            };
            _dbContext.UserLessonProgress.Add(progress);
        }
        else
        {
            var wasCompleted = progress.IsCompleted;
            progress.Score = Math.Max(progress.Score, score); // Keep best score
            progress.MaxScore = maxScore;
            progress.AttemptsCount++;
            progress.LastAttemptAt = now;
            
            if (!wasCompleted && score > 0)
            {
                progress.IsCompleted = true;
                progress.CompletedAt = now;
            }
        }

        await _dbContext.SaveChangesAsync();

        // Get lesson details for event
        var lesson = await GetLessonAsync();
        if (lesson != null && isFirstCompletion && score > 0)
        {
            // Publish lesson completed event
            await _messageBus.PublishAsync(new LessonCompletedEvent(
                userId, lessonId, lesson.CourseId, score, maxScore, 
                lesson.ExperiencePoints, now));

            // Update user's experience points and streak
            var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
            await userGrain.AddExperiencePointsAsync(lesson.ExperiencePoints, "lesson_completion");
            await userGrain.UpdateStreakAsync();

            // Check for first lesson badge
            if (isFirstCompletion)
            {
                var badge = new Badge
                {
                    Id = "first_lesson",
                    Name = "First Steps",
                    Description = "Completed your first lesson",
                    IconUrl = "/badges/first_lesson.png",
                    EarnedAt = now,
                    Type = BadgeType.FirstLesson
                };
                await userGrain.AddBadgeAsync(badge);
            }

            _logger.LogInformation("User {UserId} completed lesson {LessonId} with score {Score}/{MaxScore}", 
                userId, lessonId, score, maxScore);
        }
    }
}
