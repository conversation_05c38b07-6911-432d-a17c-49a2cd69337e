using Lingo.Core.Events;
using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Marten;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Orleans;
using System.Text.Json;
using Wolverine;

namespace Lingo.Infrastructure.Orleans;

public class UserGrain : Grain, IUserGrain
{
    private readonly IDocumentSession _documentSession;
    private readonly IDistributedCache _cache;
    private readonly IMessageBus _messageBus;
    private readonly ILogger<UserGrain> _logger;
    private User? _user;

    public UserGrain(
        IDocumentSession documentSession,
        IDistributedCache cache,
        IMessageBus messageBus,
        ILogger<UserGrain> logger)
    {
        _documentSession = documentSession;
        _cache = cache;
        _messageBus = messageBus;
        _logger = logger;
    }

    public async Task<User?> GetUserAsync()
    {
        if (_user != null) return _user;

        var userId = this.GetPrimaryKeyLong();
        var cacheKey = $"user:{userId}";

        // Try cache first
        var cachedUser = await _cache.GetStringAsync(cacheKey);
        if (!string.IsNullOrEmpty(cachedUser))
        {
            _user = JsonSerializer.Deserialize<User>(cachedUser);
            return _user;
        }

        // Load from Marten
        _user = await _documentSession.LoadAsync<User>(userId);
        
        if (_user != null)
        {
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(_user), 
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(30) });
        }

        return _user;
    }

    public async Task<User> CreateUserAsync(string email, string displayName)
    {
        var userId = this.GetPrimaryKeyLong();
        
        _user = new User
        {
            Id = userId,
            Email = email,
            DisplayName = displayName,
            ExperiencePoints = 0,
            CurrentStreak = 0,
            LongestStreak = 0,
            LastActivityDate = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            Badges = new List<Badge>(),
            CourseProgress = new List<UserCourseProgress>()
        };

        _documentSession.Store(_user);
        await _documentSession.SaveChangesAsync();

        // Cache the user
        var cacheKey = $"user:{userId}";
        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(_user),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(30) });

        // Publish event
        await _messageBus.PublishAsync(new UserRegisteredEvent(userId, email, displayName, DateTime.UtcNow));

        _logger.LogInformation("Created new user {UserId} with email {Email}", userId, email);
        return _user;
    }

    public async Task UpdateUserAsync(User user)
    {
        _user = user;
        _documentSession.Store(_user);
        await _documentSession.SaveChangesAsync();

        // Update cache
        var cacheKey = $"user:{user.Id}";
        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(_user),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(30) });
    }

    public async Task AddExperiencePointsAsync(int points, string source)
    {
        var user = await GetUserAsync();
        if (user == null) return;

        user.ExperiencePoints += points;
        user.LastActivityDate = DateTime.UtcNow;

        await UpdateUserAsync(user);

        // Publish event
        await _messageBus.PublishAsync(new ExperiencePointsEarnedEvent(
            user.Id, points, user.ExperiencePoints, source, DateTime.UtcNow));

        // Check for XP-based badges
        await CheckExperienceBadgesAsync(user);
    }

    public async Task UpdateStreakAsync()
    {
        var user = await GetUserAsync();
        if (user == null) return;

        var today = DateTime.UtcNow.Date;
        var lastActivity = user.LastActivityDate.Date;

        if (lastActivity == today.AddDays(-1))
        {
            // Consecutive day
            user.CurrentStreak++;
            if (user.CurrentStreak > user.LongestStreak)
            {
                user.LongestStreak = user.CurrentStreak;
            }
        }
        else if (lastActivity != today)
        {
            // Streak broken
            user.CurrentStreak = 1;
        }

        user.LastActivityDate = DateTime.UtcNow;
        await UpdateUserAsync(user);

        // Publish event
        await _messageBus.PublishAsync(new StreakUpdatedEvent(
            user.Id, user.CurrentStreak, user.LongestStreak, DateTime.UtcNow));

        // Check for streak badges
        await CheckStreakBadgesAsync(user);
    }

    public async Task AddBadgeAsync(Badge badge)
    {
        var user = await GetUserAsync();
        if (user == null) return;

        if (!user.Badges.Any(b => b.Id == badge.Id))
        {
            user.Badges.Add(badge);
            await UpdateUserAsync(user);

            // Publish event
            await _messageBus.PublishAsync(new BadgeEarnedEvent(
                user.Id, badge.Id, badge.Name, badge.Type, badge.EarnedAt));

            _logger.LogInformation("User {UserId} earned badge {BadgeName}", user.Id, badge.Name);
        }
    }

    public async Task<List<Badge>> GetBadgesAsync()
    {
        var user = await GetUserAsync();
        return user?.Badges ?? new List<Badge>();
    }

    public async Task<UserCourseProgress?> GetCourseProgressAsync(long courseId)
    {
        var user = await GetUserAsync();
        return user?.CourseProgress.FirstOrDefault(cp => cp.CourseId == courseId);
    }

    public async Task UpdateCourseProgressAsync(long courseId, int completedLessons, int totalLessons)
    {
        var user = await GetUserAsync();
        if (user == null) return;

        var progress = user.CourseProgress.FirstOrDefault(cp => cp.CourseId == courseId);
        if (progress == null)
        {
            progress = new UserCourseProgress
            {
                UserId = user.Id,
                CourseId = courseId,
                CompletedLessons = completedLessons,
                TotalLessons = totalLessons,
                LastAccessedAt = DateTime.UtcNow,
                IsCompleted = completedLessons >= totalLessons
            };
            user.CourseProgress.Add(progress);
        }
        else
        {
            progress.CompletedLessons = completedLessons;
            progress.TotalLessons = totalLessons;
            progress.LastAccessedAt = DateTime.UtcNow;
            progress.IsCompleted = completedLessons >= totalLessons;
        }

        await UpdateUserAsync(user);

        // Check if course is completed
        if (progress.IsCompleted)
        {
            // This would need course information - simplified for now
            await _messageBus.PublishAsync(new CourseCompletedEvent(
                user.Id, courseId, "Course", DifficultyLevel.Beginner, DateTime.UtcNow));
        }
    }

    private async Task CheckExperienceBadgesAsync(User user)
    {
        var milestones = new[] { 100, 500, 1000, 2500, 5000, 10000 };
        
        foreach (var milestone in milestones)
        {
            if (user.ExperiencePoints >= milestone)
            {
                var badgeId = $"xp_{milestone}";
                if (!user.Badges.Any(b => b.Id == badgeId))
                {
                    var badge = new Badge
                    {
                        Id = badgeId,
                        Name = $"{milestone} XP Master",
                        Description = $"Earned {milestone} experience points",
                        IconUrl = $"/badges/xp_{milestone}.png",
                        EarnedAt = DateTime.UtcNow,
                        Type = BadgeType.ExperiencePoints
                    };
                    await AddBadgeAsync(badge);
                }
            }
        }
    }

    private async Task CheckStreakBadgesAsync(User user)
    {
        var streakMilestones = new[] { 3, 7, 14, 30, 100 };
        
        foreach (var milestone in streakMilestones)
        {
            if (user.CurrentStreak >= milestone)
            {
                var badgeId = $"streak_{milestone}";
                if (!user.Badges.Any(b => b.Id == badgeId))
                {
                    var badge = new Badge
                    {
                        Id = badgeId,
                        Name = $"{milestone} Day Streak",
                        Description = $"Maintained a {milestone} day learning streak",
                        IconUrl = $"/badges/streak_{milestone}.png",
                        EarnedAt = DateTime.UtcNow,
                        Type = BadgeType.Streak
                    };
                    await AddBadgeAsync(badge);
                }
            }
        }
    }
}
