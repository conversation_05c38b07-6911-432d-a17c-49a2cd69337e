using Lingo.Core.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net.Http.Json;
using Xunit;

namespace Lingo.Tests.Controllers;

public class CoursesControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public CoursesControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetCourses_ReturnsSuccessStatusCode()
    {
        // Act
        var response = await _client.GetAsync("/api/courses");

        // Assert
        response.EnsureSuccessStatusCode();
    }

    [Fact]
    public async Task GetCourses_ReturnsListOfCourses()
    {
        // Act
        var courses = await _client.GetFromJsonAsync<List<Course>>("/api/courses");

        // Assert
        Assert.NotNull(courses);
        Assert.NotEmpty(courses);
    }

    [Fact]
    public async Task GetCourse_WithValidId_ReturnsCourse()
    {
        // Arrange
        var courses = await _client.GetFromJsonAsync<List<Course>>("/api/courses");
        Assert.NotNull(courses);
        Assert.NotEmpty(courses);
        
        var firstCourse = courses.First();

        // Act
        var course = await _client.GetFromJsonAsync<Course>($"/api/courses/{firstCourse.Id}");

        // Assert
        Assert.NotNull(course);
        Assert.Equal(firstCourse.Id, course.Id);
        Assert.Equal(firstCourse.Name, course.Name);
    }

    [Fact]
    public async Task GetCourse_WithInvalidId_ReturnsNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/courses/999999");

        // Assert
        Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
    }
}
