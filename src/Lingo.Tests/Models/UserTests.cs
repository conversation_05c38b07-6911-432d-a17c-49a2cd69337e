using Lingo.Core.Models;
using Xunit;

namespace Lingo.Tests.Models;

public class UserTests
{
    [Fact]
    public void User_DefaultValues_AreSetCorrectly()
    {
        // Act
        var user = new User();

        // Assert
        Assert.Equal(0, user.Id);
        Assert.Equal(string.Empty, user.Email);
        Assert.Equal(string.Empty, user.DisplayName);
        Assert.Equal(0, user.ExperiencePoints);
        Assert.Equal(0, user.CurrentStreak);
        Assert.Equal(0, user.LongestStreak);
        Assert.NotNull(user.Badges);
        Assert.Empty(user.Badges);
        Assert.NotNull(user.CourseProgress);
        Assert.Empty(user.CourseProgress);
    }

    [Fact]
    public void UserCourseProgress_CompletionPercentage_CalculatedCorrectly()
    {
        // Arrange
        var progress = new UserCourseProgress
        {
            CompletedLessons = 15,
            TotalLessons = 25
        };

        // Act
        var percentage = progress.CompletionPercentage;

        // Assert
        Assert.Equal(60.0, percentage);
    }

    [Fact]
    public void UserCourseProgress_CompletionPercentage_WithZeroTotal_ReturnsZero()
    {
        // Arrange
        var progress = new UserCourseProgress
        {
            CompletedLessons = 5,
            TotalLessons = 0
        };

        // Act
        var percentage = progress.CompletionPercentage;

        // Assert
        Assert.Equal(0.0, percentage);
    }

    [Fact]
    public void Badge_DefaultValues_AreSetCorrectly()
    {
        // Act
        var badge = new Badge();

        // Assert
        Assert.Equal(string.Empty, badge.Id);
        Assert.Equal(string.Empty, badge.Name);
        Assert.Equal(string.Empty, badge.Description);
        Assert.Equal(string.Empty, badge.IconUrl);
        Assert.Equal(BadgeType.LessonCompletion, badge.Type);
    }
}
