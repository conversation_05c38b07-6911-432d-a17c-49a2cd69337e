
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197 d17.13
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Lingo", "Lingo\Lingo\Lingo.csproj", "{1E95AF28-5B86-C854-C28D-59115237489F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Lingo.Client", "Lingo\Lingo.Client\Lingo.Client.csproj", "{840C9D27-8A7E-8DAF-05B8-279C8AB16985}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Lingo.AppHost", "Lingo.AppHost\Lingo.AppHost.csproj", "{0BAB917B-A72E-4F5E-83D4-300EA60DEE23}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Lingo.ServiceDefaults", "Lingo.ServiceDefaults\Lingo.ServiceDefaults.csproj", "{23D78717-B3CA-C37E-9E70-9371C83FCBF9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "__Misc__", "__Misc__", "{9A73CE3F-9736-45C5-B554-96529DB25B93}"
	ProjectSection(SolutionItems) = preProject
		..\.gitignore = ..\.gitignore
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Aspire", "Aspire", "{A1D719CD-4DBB-430A-BC79-93F50F974E33}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Electra", "Electra", "{63F84B75-9563-40BB-9BA0-35DF46AC851C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1E95AF28-5B86-C854-C28D-59115237489F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E95AF28-5B86-C854-C28D-59115237489F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E95AF28-5B86-C854-C28D-59115237489F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E95AF28-5B86-C854-C28D-59115237489F}.Release|Any CPU.Build.0 = Release|Any CPU
		{840C9D27-8A7E-8DAF-05B8-279C8AB16985}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{840C9D27-8A7E-8DAF-05B8-279C8AB16985}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{840C9D27-8A7E-8DAF-05B8-279C8AB16985}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{840C9D27-8A7E-8DAF-05B8-279C8AB16985}.Release|Any CPU.Build.0 = Release|Any CPU
		{0BAB917B-A72E-4F5E-83D4-300EA60DEE23}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0BAB917B-A72E-4F5E-83D4-300EA60DEE23}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0BAB917B-A72E-4F5E-83D4-300EA60DEE23}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0BAB917B-A72E-4F5E-83D4-300EA60DEE23}.Release|Any CPU.Build.0 = Release|Any CPU
		{23D78717-B3CA-C37E-9E70-9371C83FCBF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23D78717-B3CA-C37E-9E70-9371C83FCBF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23D78717-B3CA-C37E-9E70-9371C83FCBF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23D78717-B3CA-C37E-9E70-9371C83FCBF9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C26B9457-2F42-4B2D-AF20-05658201E894}
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{0BAB917B-A72E-4F5E-83D4-300EA60DEE23} = {A1D719CD-4DBB-430A-BC79-93F50F974E33}
		{23D78717-B3CA-C37E-9E70-9371C83FCBF9} = {A1D719CD-4DBB-430A-BC79-93F50F974E33}
	EndGlobalSection
EndGlobal
