@page "/courses/{CourseId:long}"
@using Lingo.Core.Models
@inject HttpClient Http
@inject NavigationManager Navigation

<PageTitle>@(course?.Name ?? "Course") - Lingo</PageTitle>

@if (course == null)
{
    <div class="loading">Loading course...</div>
}
else
{
    <div class="course-header">
        <div class="course-info">
            <img src="@course.ImageUrl" alt="@course.Language" class="course-flag" />
            <div class="course-details">
                <h1>@course.Name</h1>
                <p>@course.Description</p>
                <div class="course-meta">
                    <span class="difficulty @GetDifficultyClass()">@course.Difficulty</span>
                    <span class="lesson-count">@course.Lessons.Count lessons</span>
                </div>
            </div>
        </div>
        
        @if (progress != null)
        {
            <div class="progress-section">
                <div class="progress-circle">
                    <div class="progress-text">
                        <span class="percentage">@((int)progress.CompletionPercentage)%</span>
                        <span class="label">Complete</span>
                    </div>
                </div>
                <div class="progress-stats">
                    <div class="stat">
                        <span class="stat-value">@progress.CompletedLessons</span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">@(progress.TotalLessons - progress.CompletedLessons)</span>
                        <span class="stat-label">Remaining</span>
                    </div>
                </div>
            </div>
        }
    </div>

    <div class="lessons-section">
        <h2>Lessons</h2>
        
        @if (userLessonProgress == null)
        {
            <div class="loading">Loading progress...</div>
        }
        else
        {
            <div class="lessons-list">
                @for (int i = 0; i < course.Lessons.Count; i++)
                {
                    var lesson = course.Lessons[i];
                    var lessonProgress = GetLessonProgress(lesson.Id);
                    var isLocked = IsLessonLocked(i, lessonProgress);
                    
                    <Lingo.Client.Components.LessonCard 
                        Lesson="lesson" 
                        Progress="lessonProgress" 
                        IsLocked="isLocked" />
                }
            </div>
        }
    </div>
}

@code {
    [Parameter] public long CourseId { get; set; }
    
    private Course? course;
    private UserCourseProgress? progress;
    private List<UserLessonProgress> userLessonProgress = new();
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        await LoadCourse();
        await LoadUserProgress();
        await LoadUserLessonProgress();
    }

    private async Task LoadCourse()
    {
        try
        {
            course = await Http.GetFromJsonAsync<Course>($"api/courses/{CourseId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading course: {ex.Message}");
        }
    }

    private async Task LoadUserProgress()
    {
        try
        {
            progress = await Http.GetFromJsonAsync<UserCourseProgress>($"api/users/{currentUserId}/courses/{CourseId}/progress");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading user progress: {ex.Message}");
        }
    }

    private async Task LoadUserLessonProgress()
    {
        try
        {
            // In a real app, you'd have an endpoint to get all lesson progress for a course
            userLessonProgress = new List<UserLessonProgress>();
            
            if (course?.Lessons != null)
            {
                foreach (var lesson in course.Lessons)
                {
                    try
                    {
                        var lessonProgress = await Http.GetFromJsonAsync<UserLessonProgress>($"api/lessons/{lesson.Id}/users/{currentUserId}/progress");
                        if (lessonProgress != null)
                        {
                            userLessonProgress.Add(lessonProgress);
                        }
                    }
                    catch
                    {
                        // Lesson not started yet
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading lesson progress: {ex.Message}");
        }
    }

    private UserLessonProgress? GetLessonProgress(long lessonId)
    {
        return userLessonProgress.FirstOrDefault(p => p.LessonId == lessonId);
    }

    private bool IsLessonLocked(int lessonIndex, UserLessonProgress? lessonProgress)
    {
        // First lesson is always unlocked
        if (lessonIndex == 0) return false;
        
        // Check if previous lesson is completed
        if (course?.Lessons != null && lessonIndex > 0)
        {
            var previousLesson = course.Lessons[lessonIndex - 1];
            var previousProgress = GetLessonProgress(previousLesson.Id);
            return previousProgress == null || !previousProgress.IsCompleted;
        }
        
        return true;
    }

    private string GetDifficultyClass()
    {
        return course?.Difficulty switch
        {
            DifficultyLevel.Beginner => "difficulty-beginner",
            DifficultyLevel.Intermediate => "difficulty-intermediate",
            DifficultyLevel.Advanced => "difficulty-advanced",
            _ => "difficulty-beginner"
        };
    }
}

<style>
    .course-header {
        background: linear-gradient(135deg, #1cb0f6, #58cc02);
        color: white;
        padding: 40px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;
    }

    .course-info {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .course-flag {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
        border: 3px solid white;
    }

    .course-details h1 {
        margin: 0 0 10px 0;
        font-size: 2.5em;
        font-weight: 700;
    }

    .course-details p {
        margin: 0 0 15px 0;
        font-size: 1.2em;
        opacity: 0.9;
    }

    .course-meta {
        display: flex;
        gap: 20px;
        align-items: center;
    }

    .difficulty {
        padding: 6px 16px;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: 600;
        text-transform: uppercase;
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .lesson-count {
        font-size: 1em;
        opacity: 0.9;
    }

    .progress-section {
        display: flex;
        align-items: center;
        gap: 30px;
    }

    .progress-circle {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid rgba(255,255,255,0.3);
    }

    .progress-text {
        text-align: center;
    }

    .percentage {
        display: block;
        font-size: 1.5em;
        font-weight: 700;
    }

    .label {
        font-size: 0.8em;
        opacity: 0.8;
    }

    .progress-stats {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .stat {
        text-align: center;
    }

    .stat-value {
        display: block;
        font-size: 1.8em;
        font-weight: 700;
    }

    .stat-label {
        font-size: 0.9em;
        opacity: 0.8;
    }

    .lessons-section {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .lessons-section h2 {
        color: #3c3c3c;
        margin-bottom: 30px;
        font-size: 2em;
        font-weight: 700;
    }

    .lessons-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .loading {
        text-align: center;
        padding: 40px;
        font-size: 1.2em;
        color: #777;
    }

    @@media (max-width: 768px) {
        .course-header {
            flex-direction: column;
            gap: 30px;
            text-align: center;
        }
        
        .course-info {
            flex-direction: column;
            text-align: center;
        }
        
        .progress-section {
            justify-content: center;
        }
    }
</style>
