@page "/"
@using Lingo.Core.Models
@inject HttpClient Http
@inject NavigationManager Navigation

<PageTitle>Lingo - Learn Languages</PageTitle>

<div class="hero-section">
    <div class="hero-content">
        <h1>Welcome to Lingo</h1>
        <p>Learn languages the fun and effective way</p>
        
        <div class="auth-buttons">
            <button class="btn btn-google" @onclick="LoginWithGoogle">
                <img src="/images/google-icon.png" alt="Google" />
                Continue with Google
            </button>
            <button class="btn btn-facebook" @onclick="LoginWithFacebook">
                <img src="/images/facebook-icon.png" alt="Facebook" />
                Continue with Facebook
            </button>
        </div>
        
        <div class="demo-section">
            <p>Or explore our courses:</p>
            <button class="btn btn-demo" @onclick="ShowCourses">View Available Courses</button>
        </div>
    </div>
</div>

@if (showCourses)
{
    <div class="courses-section">
        <h2>Choose Your Language</h2>
        
        @if (courses == null)
        {
            <div class="loading">Loading courses...</div>
        }
        else if (courses.Any())
        {
            <div class="courses-grid">
                @foreach (var course in courses)
                {
                    <Lingo.Client.Components.CourseCard Course="course" Progress="GetCourseProgress(course.Id)" />
                }
            </div>
        }
        else
        {
            <p>No courses available at the moment.</p>
        }
    </div>
}

@code {
    private List<Course>? courses;
    private List<UserCourseProgress> userProgress = new();
    private bool showCourses = false;
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        // In a real app, you'd check if user is authenticated
        await LoadUserProgress();
    }

    private async Task ShowCourses()
    {
        showCourses = true;
        if (courses == null)
        {
            await LoadCourses();
        }
    }

    private async Task LoadCourses()
    {
        try
        {
            courses = await Http.GetFromJsonAsync<List<Course>>("api/courses");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading courses: {ex.Message}");
            courses = new List<Course>();
        }
    }

    private async Task LoadUserProgress()
    {
        try
        {
            userProgress = await Http.GetFromJsonAsync<List<UserCourseProgress>>($"api/users/{currentUserId}/progress") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading user progress: {ex.Message}");
            userProgress = new List<UserCourseProgress>();
        }
    }

    private UserCourseProgress? GetCourseProgress(long courseId)
    {
        return userProgress.FirstOrDefault(p => p.CourseId == courseId);
    }

    private void LoginWithGoogle()
    {
        // Placeholder for Google OAuth
        Navigation.NavigateTo("/Account/Login");
    }

    private void LoginWithFacebook()
    {
        // Placeholder for Facebook OAuth
        Navigation.NavigateTo("/Account/Login");
    }
}

<style>
    .hero-section {
        background: linear-gradient(135deg, #58cc02, #89e219);
        color: white;
        padding: 80px 20px;
        text-align: center;
        margin-bottom: 40px;
    }

    .hero-content h1 {
        font-size: 3em;
        margin-bottom: 20px;
        font-weight: 700;
    }

    .hero-content p {
        font-size: 1.3em;
        margin-bottom: 40px;
        opacity: 0.9;
    }

    .auth-buttons {
        display: flex;
        flex-direction: column;
        gap: 16px;
        max-width: 300px;
        margin: 0 auto 40px;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 1em;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }

    .btn-google {
        background: white;
        color: #333;
        border: 1px solid #ddd;
    }

    .btn-google:hover {
        background: #f8f9fa;
        transform: translateY(-2px);
    }

    .btn-facebook {
        background: #1877f2;
        color: white;
    }

    .btn-facebook:hover {
        background: #166fe5;
        transform: translateY(-2px);
    }

    .btn-demo {
        background: rgba(255,255,255,0.2);
        color: white;
        border: 2px solid white;
    }

    .btn-demo:hover {
        background: white;
        color: #58cc02;
    }

    .demo-section p {
        margin-bottom: 20px;
        font-size: 1.1em;
    }

    .courses-section {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .courses-section h2 {
        text-align: center;
        color: #3c3c3c;
        margin-bottom: 40px;
        font-size: 2.5em;
        font-weight: 700;
    }

    .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .loading {
        text-align: center;
        padding: 40px;
        font-size: 1.2em;
        color: #777;
    }

    @@media (max-width: 768px) {
        .hero-content h1 {
            font-size: 2em;
        }
        
        .courses-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
