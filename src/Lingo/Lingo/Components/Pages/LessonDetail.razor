@page "/lessons/{LessonId:long}"
@using Lingo.Core.Models
@inject HttpClient Http
@inject NavigationManager Navigation

<PageTitle>@(lesson?.Title ?? "Lesson") - Lingo</PageTitle>

@if (lesson == null || questions == null)
{
    <div class="loading">Loading lesson...</div>
}
else
{
    <div class="lesson-container">
        <div class="lesson-header">
            <button class="back-button" @onclick="GoBack">← Back</button>
            <div class="lesson-info">
                <h1>@lesson.Title</h1>
                <p>@lesson.Description</p>
            </div>
            <div class="lesson-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: @(GetProgressPercentage())%"></div>
                </div>
                <span class="progress-text">@(currentQuestionIndex + 1) / @questions.Count</span>
            </div>
        </div>

        @if (!isCompleted)
        {
            <div class="question-container">
                @if (currentQuestionIndex < questions.Count)
                {
                    var question = questions[currentQuestionIndex];
                    
                    <div class="question-card">
                        <div class="question-header">
                            <h2>@question.QuestionText</h2>
                            @if (!string.IsNullOrEmpty(question.AudioUrl))
                            {
                                <button class="audio-button" @onclick="() => PlayAudio(question.AudioUrl)">🔊</button>
                            }
                        </div>
                        
                        @if (!string.IsNullOrEmpty(question.ImageUrl))
                        {
                            <div class="question-image">
                                <img src="@question.ImageUrl" alt="Question image" />
                            </div>
                        }
                        
                        <div class="options-container">
                            @foreach (var option in question.Options.OrderBy(o => o.OrderIndex))
                            {
                                <button class="option-button @GetOptionClass(option.Id)" 
                                        @onclick="() => SelectOption(question.Id, option.Id)"
                                        disabled="@(hasAnswered && selectedAnswers.ContainsKey(question.Id))">
                                    @option.OptionText
                                    @if (!string.IsNullOrEmpty(option.AudioUrl))
                                    {
                                        <span class="option-audio" @onclick:stopPropagation="true" @onclick="() => PlayAudio(option.AudioUrl)">🔊</span>
                                    }
                                </button>
                            }
                        </div>
                        
                        @if (hasAnswered && selectedAnswers.ContainsKey(question.Id))
                        {
                            <div class="answer-feedback @(IsAnswerCorrect(question) ? "correct" : "incorrect")">
                                @if (IsAnswerCorrect(question))
                                {
                                    <div class="feedback-content">
                                        <span class="feedback-icon">✓</span>
                                        <span class="feedback-text">Correct!</span>
                                    </div>
                                }
                                else
                                {
                                    <div class="feedback-content">
                                        <span class="feedback-icon">✗</span>
                                        <span class="feedback-text">Incorrect. The correct answer is: @GetCorrectAnswerText(question)</span>
                                    </div>
                                }
                            </div>
                        }
                        
                        <div class="question-actions">
                            @if (!hasAnswered || !selectedAnswers.ContainsKey(question.Id))
                            {
                                <button class="btn btn-primary" @onclick="CheckAnswer" disabled="@(!CanCheckAnswer(question.Id))">
                                    Check Answer
                                </button>
                            }
                            else
                            {
                                <button class="btn btn-primary" @onclick="NextQuestion">
                                    @(currentQuestionIndex < questions.Count - 1 ? "Next Question" : "Complete Lesson")
                                </button>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="completion-screen">
                <div class="completion-content">
                    <div class="completion-icon">🎉</div>
                    <h2>Lesson Complete!</h2>
                    <div class="score-display">
                        <span class="score">@correctAnswers / @questions.Count</span>
                        <span class="score-label">Correct Answers</span>
                    </div>
                    <div class="xp-earned">
                        <span class="xp-amount">+@lesson.ExperiencePoints XP</span>
                    </div>
                    
                    @if (earnedBadges.Any())
                    {
                        <div class="badges-earned">
                            <h3>Badges Earned!</h3>
                            <div class="badges-list">
                                @foreach (var badge in earnedBadges)
                                {
                                    <div class="badge-item">
                                        <img src="@badge.IconUrl" alt="@badge.Name" />
                                        <span>@badge.Name</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    
                    <div class="completion-actions">
                        <button class="btn btn-primary" @onclick="ContinueLearning">Continue Learning</button>
                        <button class="btn btn-secondary" @onclick="GoToCourse">Back to Course</button>
                    </div>
                </div>
            </div>
        }
    </div>
}

@code {
    [Parameter] public long LessonId { get; set; }
    
    private Lesson? lesson;
    private List<LessonQuestion> questions = new();
    private Dictionary<long, List<long>> selectedAnswers = new();
    private int currentQuestionIndex = 0;
    private bool hasAnswered = false;
    private bool isCompleted = false;
    private int correctAnswers = 0;
    private List<Badge> earnedBadges = new();
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        await LoadLesson();
        await LoadQuestions();
    }

    private async Task LoadLesson()
    {
        try
        {
            lesson = await Http.GetFromJsonAsync<Lesson>($"api/lessons/{LessonId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading lesson: {ex.Message}");
        }
    }

    private async Task LoadQuestions()
    {
        try
        {
            questions = await Http.GetFromJsonAsync<List<LessonQuestion>>($"api/lessons/{LessonId}/questions") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading questions: {ex.Message}");
        }
    }

    private void SelectOption(long questionId, long optionId)
    {
        if (hasAnswered) return;
        
        if (!selectedAnswers.ContainsKey(questionId))
        {
            selectedAnswers[questionId] = new List<long>();
        }
        
        var currentSelections = selectedAnswers[questionId];
        
        // For now, assume single selection (can be extended for multiple choice)
        currentSelections.Clear();
        currentSelections.Add(optionId);
    }

    private bool CanCheckAnswer(long questionId)
    {
        return selectedAnswers.ContainsKey(questionId) && selectedAnswers[questionId].Any();
    }

    private void CheckAnswer()
    {
        hasAnswered = true;
        StateHasChanged();
    }

    private void NextQuestion()
    {
        if (currentQuestionIndex < questions.Count - 1)
        {
            currentQuestionIndex++;
            hasAnswered = false;
        }
        else
        {
            CompleteLesson();
        }
    }

    private async Task CompleteLesson()
    {
        // Calculate score
        correctAnswers = 0;
        foreach (var question in questions)
        {
            if (IsAnswerCorrect(question))
            {
                correctAnswers++;
            }
        }

        // Submit answers to server
        try
        {
            var submitRequest = new
            {
                UserId = currentUserId,
                Answers = selectedAnswers
            };

            var response = await Http.PostAsJsonAsync($"api/lessons/{LessonId}/submit", submitRequest);
            if (response.IsSuccessStatusCode)
            {
                // Load any earned badges
                await LoadEarnedBadges();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error submitting lesson: {ex.Message}");
        }

        isCompleted = true;
    }

    private async Task LoadEarnedBadges()
    {
        try
        {
            earnedBadges = await Http.GetFromJsonAsync<List<Badge>>($"api/users/{currentUserId}/badges") ?? new();
            // In a real app, you'd only show newly earned badges
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading badges: {ex.Message}");
        }
    }

    private bool IsAnswerCorrect(LessonQuestion question)
    {
        if (!selectedAnswers.ContainsKey(question.Id)) return false;
        
        var userAnswers = selectedAnswers[question.Id];
        var correctOptions = question.Options.Where(o => o.IsCorrect).Select(o => o.Id).ToList();
        
        return userAnswers.Count == correctOptions.Count && 
               userAnswers.All(ua => correctOptions.Contains(ua)) &&
               correctOptions.All(co => userAnswers.Contains(co));
    }

    private string GetCorrectAnswerText(LessonQuestion question)
    {
        var correctOption = question.Options.FirstOrDefault(o => o.IsCorrect);
        return correctOption?.OptionText ?? "Unknown";
    }

    private string GetOptionClass(long optionId)
    {
        var question = questions[currentQuestionIndex];
        if (!hasAnswered || !selectedAnswers.ContainsKey(question.Id)) 
        {
            var isSelected = selectedAnswers.ContainsKey(question.Id) && selectedAnswers[question.Id].Contains(optionId);
            return isSelected ? "selected" : "";
        }
        
        var option = question.Options.FirstOrDefault(o => o.Id == optionId);
        var isUserSelection = selectedAnswers[question.Id].Contains(optionId);
        
        if (option?.IsCorrect == true)
        {
            return "correct";
        }
        else if (isUserSelection)
        {
            return "incorrect";
        }
        
        return "";
    }

    private double GetProgressPercentage()
    {
        if (questions.Count == 0) return 0;
        return ((double)(currentQuestionIndex + 1) / questions.Count) * 100;
    }

    private void PlayAudio(string audioUrl)
    {
        // In a real app, you'd implement audio playback
        Console.WriteLine($"Playing audio: {audioUrl}");
    }

    private void GoBack()
    {
        Navigation.NavigateTo($"/courses/{lesson?.CourseId}");
    }

    private void ContinueLearning()
    {
        Navigation.NavigateTo($"/courses/{lesson?.CourseId}");
    }

    private void GoToCourse()
    {
        Navigation.NavigateTo($"/courses/{lesson?.CourseId}");
    }
}
