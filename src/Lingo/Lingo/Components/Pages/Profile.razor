@page "/profile"
@using Lingo.Core.Models
@inject HttpClient Http
@inject NavigationManager Navigation

<PageTitle>Profile - Lingo</PageTitle>

<div class="profile-container">
    @if (user == null)
    {
        <div class="loading">Loading profile...</div>
    }
    else
    {
        <div class="profile-header">
            <div class="profile-avatar">
                @if (!string.IsNullOrEmpty(user.ProfileImageUrl))
                {
                    <img src="@user.ProfileImageUrl" alt="@user.DisplayName" />
                }
                else
                {
                    <div class="avatar-placeholder">@GetInitials(user.DisplayName)</div>
                }
            </div>
            <div class="profile-info">
                <h1>@user.DisplayName</h1>
                <p class="email">@user.Email</p>
                <div class="profile-stats">
                    <div class="stat">
                        <span class="stat-value">@user.ExperiencePoints</span>
                        <span class="stat-label">Total XP</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">@user.CurrentStreak</span>
                        <span class="stat-label">Day Streak</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">@user.LongestStreak</span>
                        <span class="stat-label">Best Streak</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="profile-content">
            <div class="section">
                <h2>Badges (@user.Badges.Count)</h2>
                @if (user.Badges.Any())
                {
                    <div class="badges-grid">
                        @foreach (var badge in user.Badges.OrderByDescending(b => b.EarnedAt))
                        {
                            <div class="badge-card">
                                <img src="@badge.IconUrl" alt="@badge.Name" />
                                <h3>@badge.Name</h3>
                                <p>@badge.Description</p>
                                <span class="earned-date">Earned @badge.EarnedAt.ToString("MMM dd, yyyy")</span>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <p>No badges earned yet. Complete lessons to earn your first badge!</p>
                    </div>
                }
            </div>

            <div class="section">
                <h2>Course Progress</h2>
                @if (courseProgress.Any())
                {
                    <div class="progress-list">
                        @foreach (var progress in courseProgress.OrderByDescending(p => p.LastAccessedAt))
                        {
                            <div class="progress-card" @onclick="() => GoToCourse(progress.CourseId)">
                                <div class="progress-info">
                                    <h3>@GetCourseName(progress.CourseId)</h3>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: @(progress.CompletionPercentage)%"></div>
                                    </div>
                                    <div class="progress-details">
                                        <span>@progress.CompletedLessons / @progress.TotalLessons lessons</span>
                                        <span class="completion-percent">@((int)progress.CompletionPercentage)%</span>
                                    </div>
                                    <p class="last-accessed">Last accessed @progress.LastAccessedAt.ToString("MMM dd, yyyy")</p>
                                </div>
                                @if (progress.IsCompleted)
                                {
                                    <div class="completion-badge">✓ Completed</div>
                                }
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <p>No courses started yet. <a href="/">Browse available courses</a> to get started!</p>
                    </div>
                }
            </div>

            <div class="section">
                <h2>Learning Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <span class="stat-number">@GetTotalLessonsCompleted()</span>
                            <span class="stat-description">Lessons Completed</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏆</div>
                        <div class="stat-content">
                            <span class="stat-number">@user.Badges.Count</span>
                            <span class="stat-description">Badges Earned</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-content">
                            <span class="stat-number">@user.ExperiencePoints</span>
                            <span class="stat-description">Experience Points</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <span class="stat-number">@user.CurrentStreak</span>
                            <span class="stat-description">Current Streak</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private User? user;
    private List<UserCourseProgress> courseProgress = new();
    private List<Course> allCourses = new();
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        await LoadUser();
        await LoadCourseProgress();
        await LoadCourses();
    }

    private async Task LoadUser()
    {
        try
        {
            user = await Http.GetFromJsonAsync<User>($"api/users/{currentUserId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading user: {ex.Message}");
            // Create demo user if not found
            user = new User
            {
                Id = currentUserId,
                DisplayName = "Demo User",
                Email = "<EMAIL>",
                ExperiencePoints = 150,
                CurrentStreak = 3,
                LongestStreak = 7,
                Badges = new List<Badge>(),
                CourseProgress = new List<UserCourseProgress>()
            };
        }
    }

    private async Task LoadCourseProgress()
    {
        try
        {
            courseProgress = await Http.GetFromJsonAsync<List<UserCourseProgress>>($"api/users/{currentUserId}/progress") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading course progress: {ex.Message}");
        }
    }

    private async Task LoadCourses()
    {
        try
        {
            allCourses = await Http.GetFromJsonAsync<List<Course>>("api/courses") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading courses: {ex.Message}");
        }
    }

    private string GetInitials(string displayName)
    {
        if (string.IsNullOrEmpty(displayName)) return "U";
        
        var parts = displayName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return displayName[0].ToString().ToUpper();
    }

    private string GetCourseName(long courseId)
    {
        var course = allCourses.FirstOrDefault(c => c.Id == courseId);
        return course?.Name ?? "Unknown Course";
    }

    private int GetTotalLessonsCompleted()
    {
        return courseProgress.Sum(p => p.CompletedLessons);
    }

    private void GoToCourse(long courseId)
    {
        Navigation.NavigateTo($"/courses/{courseId}");
    }
}

<style>
    .profile-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
    }

    .profile-header {
        background: linear-gradient(135deg, #1cb0f6, #58cc02);
        color: white;
        padding: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 30px;
        margin-bottom: 30px;
    }

    .profile-avatar {
        flex-shrink: 0;
    }

    .profile-avatar img,
    .avatar-placeholder {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 4px solid white;
    }

    .avatar-placeholder {
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2em;
        font-weight: bold;
    }

    .profile-info h1 {
        margin: 0 0 8px 0;
        font-size: 2.5em;
        font-weight: 700;
    }

    .email {
        margin: 0 0 20px 0;
        opacity: 0.9;
        font-size: 1.1em;
    }

    .profile-stats {
        display: flex;
        gap: 30px;
    }

    .stat {
        text-align: center;
    }

    .stat-value {
        display: block;
        font-size: 2em;
        font-weight: 700;
    }

    .stat-label {
        font-size: 0.9em;
        opacity: 0.8;
    }

    .profile-content {
        display: flex;
        flex-direction: column;
        gap: 40px;
    }

    .section h2 {
        color: #3c3c3c;
        margin-bottom: 20px;
        font-size: 1.8em;
        font-weight: 700;
    }

    .badges-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .badge-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .badge-card img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 15px;
    }

    .badge-card h3 {
        margin: 0 0 8px 0;
        color: #3c3c3c;
        font-size: 1.1em;
    }

    .badge-card p {
        margin: 0 0 12px 0;
        color: #777;
        font-size: 0.9em;
        line-height: 1.4;
    }

    .earned-date {
        font-size: 0.8em;
        color: #999;
    }

    .progress-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .progress-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .progress-card:hover {
        border-color: #58cc02;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .progress-info {
        flex: 1;
    }

    .progress-info h3 {
        margin: 0 0 12px 0;
        color: #3c3c3c;
        font-size: 1.2em;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e5e5e5;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #58cc02, #89e219);
        transition: width 0.3s ease;
    }

    .progress-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.9em;
        color: #777;
    }

    .completion-percent {
        font-weight: 600;
        color: #58cc02;
    }

    .last-accessed {
        margin: 0;
        font-size: 0.8em;
        color: #999;
    }

    .completion-badge {
        background: #58cc02;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: 600;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-icon {
        font-size: 2.5em;
    }

    .stat-content {
        display: flex;
        flex-direction: column;
    }

    .stat-number {
        font-size: 1.8em;
        font-weight: 700;
        color: #3c3c3c;
    }

    .stat-description {
        font-size: 0.9em;
        color: #777;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #777;
    }

    .empty-state a {
        color: #1cb0f6;
        text-decoration: none;
    }

    .empty-state a:hover {
        text-decoration: underline;
    }

    .loading {
        text-align: center;
        padding: 40px;
        font-size: 1.2em;
        color: #777;
    }

    @@media (max-width: 768px) {
        .profile-header {
            flex-direction: column;
            text-align: center;
        }
        
        .profile-stats {
            justify-content: center;
        }
        
        .badges-grid {
            grid-template-columns: 1fr;
        }
        
        .progress-card {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
