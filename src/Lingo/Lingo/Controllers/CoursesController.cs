using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Microsoft.AspNetCore.Mvc;
using Orleans;

namespace Lingo.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CoursesController : ControllerBase
{
    private readonly IClusterClient _clusterClient;
    private readonly ILogger<CoursesController> _logger;

    public CoursesController(IClusterClient clusterClient, ILogger<CoursesController> logger)
    {
        _clusterClient = clusterClient;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<Course>>> GetCourses()
    {
        try
        {
            var learningService = _clusterClient.GetGrain<ILearningService>(0);
            var courses = await learningService.GetAvailableCoursesAsync();
            return Ok(courses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting courses");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{courseId}")]
    public async Task<ActionResult<Course>> GetCourse(long courseId)
    {
        try
        {
            var learningService = _clusterClient.GetGrain<ILearningService>(0);
            var course = await learningService.GetCourseWithLessonsAsync(courseId);
            
            if (course == null)
                return NotFound();

            return Ok(course);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting course {CourseId}", courseId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{courseId}/lessons")]
    public async Task<ActionResult<List<Lesson>>> GetCourseLessons(long courseId)
    {
        try
        {
            var courseGrain = _clusterClient.GetGrain<ICourseGrain>(courseId);
            var lessons = await courseGrain.GetLessonsAsync();
            return Ok(lessons);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting lessons for course {CourseId}", courseId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{courseId}/lessons/{lessonId}")]
    public async Task<ActionResult<Lesson>> GetLesson(long courseId, long lessonId)
    {
        try
        {
            var courseGrain = _clusterClient.GetGrain<ICourseGrain>(courseId);
            var lesson = await courseGrain.GetLessonAsync(lessonId);
            
            if (lesson == null)
                return NotFound();

            return Ok(lesson);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting lesson {LessonId} for course {CourseId}", lessonId, courseId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{courseId}/lessons/{lessonId}/questions")]
    public async Task<ActionResult<List<LessonQuestion>>> GetLessonQuestions(long courseId, long lessonId)
    {
        try
        {
            var courseGrain = _clusterClient.GetGrain<ICourseGrain>(courseId);
            var questions = await courseGrain.GetLessonQuestionsAsync(lessonId);
            return Ok(questions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting questions for lesson {LessonId}", lessonId);
            return StatusCode(500, "Internal server error");
        }
    }
}
