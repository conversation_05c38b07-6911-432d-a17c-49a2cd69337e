using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Microsoft.AspNetCore.Mvc;
using Orleans;

namespace Lingo.Controllers;

[ApiController]
[Route("api/[controller]")]
public class LessonsController : ControllerBase
{
    private readonly IClusterClient _clusterClient;
    private readonly ILogger<LessonsController> _logger;

    public LessonsController(IClusterClient clusterClient, ILogger<LessonsController> logger)
    {
        _clusterClient = clusterClient;
        _logger = logger;
    }

    [HttpGet("{lessonId}")]
    public async Task<ActionResult<Lesson>> GetLesson(long lessonId)
    {
        try
        {
            var lessonGrain = _clusterClient.GetGrain<ILessonGrain>(lessonId);
            var lesson = await lessonGrain.GetLessonAsync();
            
            if (lesson == null)
                return NotFound();

            return Ok(lesson);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting lesson {LessonId}", lessonId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{lessonId}/questions")]
    public async Task<ActionResult<List<LessonQuestion>>> GetLessonQuestions(long lessonId)
    {
        try
        {
            var lessonGrain = _clusterClient.GetGrain<ILessonGrain>(lessonId);
            var questions = await lessonGrain.GetQuestionsAsync();
            return Ok(questions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting questions for lesson {LessonId}", lessonId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{lessonId}/submit")]
    public async Task<ActionResult<UserLessonProgress>> SubmitLessonAnswers(
        long lessonId, 
        [FromBody] SubmitAnswersRequest request)
    {
        try
        {
            var learningService = _clusterClient.GetGrain<ILearningService>(0);
            var progress = await learningService.SubmitLessonAnswersAsync(
                request.UserId, lessonId, request.Answers);
            
            return Ok(progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting answers for lesson {LessonId}", lessonId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{lessonId}/users/{userId}/progress")]
    public async Task<ActionResult<UserLessonProgress>> GetUserLessonProgress(long lessonId, long userId)
    {
        try
        {
            var lessonGrain = _clusterClient.GetGrain<ILessonGrain>(lessonId);
            var progress = await lessonGrain.GetUserProgressAsync(userId);
            
            if (progress == null)
                return NotFound();

            return Ok(progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting lesson progress for user {UserId}, lesson {LessonId}", userId, lessonId);
            return StatusCode(500, "Internal server error");
        }
    }
}

public class SubmitAnswersRequest
{
    public long UserId { get; set; }
    public Dictionary<long, List<long>> Answers { get; set; } = new();
}
