using Lingo.Core.Interfaces;
using Lingo.Core.Models;
using Microsoft.AspNetCore.Mvc;
using Orleans;

namespace Lingo.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IClusterClient _clusterClient;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IClusterClient clusterClient, ILogger<UsersController> logger)
    {
        _clusterClient = clusterClient;
        _logger = logger;
    }

    [HttpGet("{userId}")]
    public async Task<ActionResult<User>> GetUser(long userId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var user = await userGrain.GetUserAsync();
            
            if (user == null)
                return NotFound();

            return Ok(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    public async Task<ActionResult<User>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            // In a real app, you'd generate a proper user ID
            var userId = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var user = await userGrain.CreateUserAsync(request.Email, request.DisplayName);
            
            return CreatedAtAction(nameof(GetUser), new { userId = user.Id }, user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{userId}/badges")]
    public async Task<ActionResult<List<Badge>>> GetUserBadges(long userId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var badges = await userGrain.GetBadgesAsync();
            return Ok(badges);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting badges for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{userId}/progress")]
    public async Task<ActionResult<List<UserCourseProgress>>> GetUserProgress(long userId)
    {
        try
        {
            var learningService = _clusterClient.GetGrain<ILearningService>(0);
            var progress = await learningService.GetUserProgressAsync(userId);
            return Ok(progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting progress for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{userId}/courses/{courseId}/progress")]
    public async Task<ActionResult<UserCourseProgress>> GetUserCourseProgress(long userId, long courseId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var progress = await userGrain.GetCourseProgressAsync(courseId);
            
            if (progress == null)
                return NotFound();

            return Ok(progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting course progress for user {UserId}, course {CourseId}", userId, courseId);
            return StatusCode(500, "Internal server error");
        }
    }
}

public class CreateUserRequest
{
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
}
