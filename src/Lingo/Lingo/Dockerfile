# Use the official .NET 9 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

# Use the official .NET 9 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files
COPY ["src/Lingo/Lingo/Lingo.csproj", "Lingo/Lingo/"]
COPY ["src/Lingo/Lingo.Client/Lingo.Client.csproj", "Lingo/Lingo.Client/"]
COPY ["src/Lingo.Core/Lingo.Core.csproj", "Lingo.Core/"]
COPY ["src/Lingo.Infrastructure/Lingo.Infrastructure.csproj", "Lingo.Infrastructure/"]
COPY ["src/Lingo.ServiceDefaults/Lingo.ServiceDefaults.csproj", "Lingo.ServiceDefaults/"]

# Restore dependencies
RUN dotnet restore "Lingo/Lingo/Lingo.csproj"

# Copy all source code
COPY src/ .

# Build the application
WORKDIR "/src/Lingo/Lingo"
RUN dotnet build "Lingo.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Publish the application
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Lingo.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Final stage - runtime image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

ENTRYPOINT ["dotnet", "Lingo.dll"]
