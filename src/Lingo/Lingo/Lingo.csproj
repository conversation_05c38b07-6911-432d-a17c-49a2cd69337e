<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-Lingo-a7e60dbf-cf9e-45d4-b852-38a10338bff1</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Marten.AspNetCore" Version="8.3.0" />
    <PackageReference Include="Weasel.Core" Version="8.1.1" />
    <PackageReference Include="Weasel.Postgresql" Version="8.1.1" />
    <PackageReference Include="WolverineFx" Version="4.3.0" />
    <PackageReference Include="WolverineFx.EntityFrameworkCore" Version="4.3.0" />
    <PackageReference Include="WolverineFx.Http" Version="4.3.0" />
    <PackageReference Include="WolverineFx.Marten" Version="4.3.0" />
    <PackageReference Include="WolverineFx.Postgresql" Version="4.3.0" />
    <PackageReference Include="WolverineFx.RDBMS" Version="4.3.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache" Version="2.3.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis" Version="2.3.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache.Serialization.SystemTextJson" Version="2.3.0" />
    <ProjectReference Include="..\..\Lingo.ServiceDefaults\Lingo.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Lingo.Client\Lingo.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6" />
  </ItemGroup>

</Project>
