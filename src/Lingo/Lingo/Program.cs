using JasperFx;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Lingo.Client.Pages;
using Lingo.Components;
using Lingo.Components.Account;
using Lingo.Data;
using Lingo.Infrastructure.Data;
using Lingo.Infrastructure.Orleans;
using Lingo.Infrastructure.EventHandlers;
using Lingo.Core.Interfaces;
using Marten;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Wolverine;
using Wolverine.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents()
    .AddAuthenticationStateSerialization();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = IdentityConstants.ApplicationScheme;
        options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
    })
    .AddIdentityCookies();

// Database configurations
builder.AddNpgsqlDataSource("lingodb");
var lingoConnectionString = builder.Configuration.GetConnectionString("lingodb") ?? "";

// Identity database (keep existing)
builder.AddNpgsqlDbContext<LingoDbContext>("lingodb");
builder.AddNpgsqlDbContext<ApplicationDbContext>(connectionName: "lingodb");
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

// Marten for document storage
builder.Services.AddMarten(options =>
{
    options.Connection(lingoConnectionString);
    options.AutoCreateSchemaObjects = AutoCreate.All;
    options.DatabaseSchemaName = "marten";
});

// Redis caching
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
});

// Orleans configuration
builder.Host.UseOrleans((context, siloBuilder) =>
{
    // if (context.HostingEnvironment.IsDevelopment())
    // {
    //     siloBuilder.UseLocalhostClustering();
    //     siloBuilder.UseInMemoryReminderService();
    //     siloBuilder.AddMemoryGrainStorageAsDefault();
    // }
    // else
    {
        siloBuilder.UseAdoNetClustering(options =>
        {
            options.ConnectionString = lingoConnectionString;
            options.Invariant = "Npgsql";
        });
        
        siloBuilder.AddReminders();
        
        siloBuilder.AddAdoNetGrainStorageAsDefault(options =>
        {
            options.ConnectionString = lingoConnectionString;
            options.Invariant = "Npgsql";
        });
    }

    siloBuilder.ConfigureServices(services =>
    {
        services.AddScoped<UserGrain>();
        services.AddScoped<CourseGrain>();
        services.AddScoped<LessonGrain>();
        services.AddScoped<LearningServiceGrain>();
    });
});

// Wolverine for messaging
builder.Host.UseWolverine(opts =>
{
    opts.UseEntityFrameworkCoreTransactions();
    opts.Services.AddScoped<UserEventHandlers>();
});

// API Controllers
builder.Services.AddControllers();

builder.Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();

builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();

// Add data seeder
builder.Services.AddScoped<DataSeeder>();

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
    app.UseMigrationsEndPoint();
    
    // Seed the database in development
    using (var scope = app.Services.CreateScope())
    {
        var seeder = scope.ServiceProvider.GetRequiredService<DataSeeder>();
        await seeder.SeedAsync();
    }
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(Lingo.Client._Imports).Assembly);

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();

// Map API controllers
app.MapControllers();

app.Run();



public partial class Program { }